"""
任务系统数据模型

定义任务配置、结果等数据结构
"""

import json
from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, field_validator

# 定义泛型类型变量
T = TypeVar("T")


class TaskConfig(BaseModel):
    """任务配置模型"""

    task_type: str
    batch_size: int = 100
    timeout: int = 3600  # 超时时间（秒）
    max_age_hours: int = 1  # 数据过期时间（小时）
    filters: Optional[Dict] = None

    # 作者监控任务特定配置
    author_video_limit: int = 50  # 每个作者最多获取的视频数量

    # 关键词监控任务特定配置
    keyword_video_limit: int = 100  # 每个关键词最多获取的视频数量
    keyword_search_days: int = 7  # 关键词搜索的天数范围

    @field_validator("task_type")
    @classmethod
    def validate_task_type(cls, v):
        allowed_types = ["trend_refresh", "author_monitor", "keyword_monitor"]
        if v not in allowed_types:
            raise ValueError(f"task_type must be one of {allowed_types}")
        return v

    @field_validator("batch_size")
    @classmethod
    def validate_batch_size(cls, v):
        if v <= 0 or v > 1000:
            raise ValueError("batch_size must be between 1 and 1000")
        return v

    @field_validator("timeout")
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0 or v > 86400:  # 最多24小时
            raise ValueError("timeout must be between 1 and 86400 seconds")
        return v

    @field_validator("max_age_hours")
    @classmethod
    def validate_max_age_hours(cls, v):
        if v <= 0 or v > 168:  # 最多7天
            raise ValueError("max_age_hours must be between 1 and 168 hours")
        return v

    @field_validator("author_video_limit")
    @classmethod
    def validate_author_video_limit(cls, v):
        if v <= 0 or v > 200:
            raise ValueError("author_video_limit must be between 1 and 200")
        return v

    @field_validator("keyword_video_limit")
    @classmethod
    def validate_keyword_video_limit(cls, v):
        if v <= 0 or v > 500:
            raise ValueError("keyword_video_limit must be between 1 and 500")
        return v

    @field_validator("keyword_search_days")
    @classmethod
    def validate_keyword_search_days(cls, v):
        if v <= 0 or v > 30:
            raise ValueError("keyword_search_days must be between 1 and 30 days")
        return v


class TaskResult(BaseModel):
    """任务执行结果"""

    task_type: str
    status: str  # "success" | "failed" | "partial"
    start_time: datetime
    end_time: datetime
    duration: float  # 执行时长（秒）
    processed_count: int  # 处理的记录数
    success_count: int  # 成功数量
    failed_count: int  # 失败数量
    errors: List[str] = []  # 错误信息列表

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.processed_count == 0:
            return 0.0
        return (self.success_count / self.processed_count) * 100


class EventPayload(BaseModel, Generic[T]):
    """事件载荷模型 - 支持泛型参数"""

    event_name: str
    event_params: T = {}  # 使用泛型类型

    @field_validator("event_name")
    @classmethod
    def validate_event_name(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError("event_name must be a non-empty string")
        return v.strip()


# 定义具体的事件参数类型
class TrendRefreshParams(BaseModel):
    """趋势刷新任务参数"""

    batch_size: int = 100
    max_age_hours: int = 1
    timeout: int = 3600
    filters: Optional[Dict[str, Any]] = None


class AuthorMonitorParams(BaseModel):
    """作者监控任务参数"""

    batch_size: int = 50
    max_age_hours: int = 2
    timeout: int = 3600
    author_video_limit: int = 50
    filters: Optional[Dict[str, Any]] = None


class KeywordMonitorParams(BaseModel):
    """关键词监控任务参数"""

    batch_size: int = 20
    max_age_hours: int = 3
    timeout: int = 3600
    keyword_video_limit: int = 100
    keyword_search_days: int = 7
    filters: Optional[Dict[str, Any]] = None


# 具体的事件载荷类型别名
TrendRefreshPayload = EventPayload[TrendRefreshParams]
AuthorMonitorPayload = EventPayload[AuthorMonitorParams]
KeywordMonitorPayload = EventPayload[KeywordMonitorParams]
GenericEventPayload = EventPayload[Dict[str, Any]]


class FunctionComputeEvent(BaseModel):
    """Function Compute 事件模型"""

    triggerTime: Optional[str] = None
    triggerName: Optional[str] = None
    payload: GenericEventPayload
    config: Optional[Dict[str, Any]] = None

    @field_validator("payload", mode="before")
    @classmethod
    def validate_payload(cls, v):
        if isinstance(v, EventPayload):
            # 如果已经是 EventPayload，检查 event_params 类型
            if hasattr(v.event_params, "model_dump"):
                # 如果是 Pydantic 模型，转换为字典
                return EventPayload(event_name=v.event_name, event_params=v.event_params.model_dump())
            return v
        elif isinstance(v, str):
            try:
                # 尝试解析 JSON 字符串
                parsed = json.loads(v)
                if isinstance(parsed, dict):
                    if "event_name" in parsed:
                        event_params = parsed.get("event_params", {})
                        return EventPayload(event_name=parsed["event_name"], event_params=event_params)
                    else:
                        # 如果没有 event_name，创建一个默认的
                        return EventPayload(event_name="unknown_event", event_params=parsed)
                else:
                    return EventPayload(event_name="string_event", event_params={"data": parsed})
            except (json.JSONDecodeError, TypeError):
                # 如果不能解析为 JSON，作为纯字符串处理
                return EventPayload(event_name="string_event", event_params={"data": v})
        elif isinstance(v, dict):
            if "event_name" in v:
                event_params = v.get("event_params", {})
                return EventPayload(event_name=v["event_name"], event_params=event_params)
            else:
                # 如果没有 event_name，尝试从其他字段推断
                task_type = v.get("task_type", "unknown")
                event_name = f"{task_type}_event" if task_type != "unknown" else "config_event"
                return EventPayload(event_name=event_name, event_params=v)
        else:
            # 其他类型，创建默认事件
            return EventPayload(event_name="default_event", event_params={"data": str(v)})


class FunctionComputeContext(BaseModel):
    """Function Compute 上下文模型"""

    request_id: str
    function_name: str = "task-scheduler"
    function_version: str = "$LATEST"
    service_name: str = "default"
    memory_limit_in_mb: str = "1024"
    time_limit_in_ms: str = "900000"


# 工厂函数，用于创建特定类型的事件载荷
def create_trend_refresh_payload(
    batch_size: int = 100, max_age_hours: int = 1, timeout: int = 3600, filters: Optional[Dict[str, Any]] = None
) -> TrendRefreshPayload:
    """创建趋势刷新事件载荷"""
    params = TrendRefreshParams(batch_size=batch_size, max_age_hours=max_age_hours, timeout=timeout, filters=filters)
    return EventPayload(event_name="trend_refresh_task", event_params=params)


def create_author_monitor_payload(
    batch_size: int = 50,
    max_age_hours: int = 2,
    timeout: int = 3600,
    author_video_limit: int = 50,
    filters: Optional[Dict[str, Any]] = None,
) -> AuthorMonitorPayload:
    """创建作者监控事件载荷"""
    params = AuthorMonitorParams(
        batch_size=batch_size,
        max_age_hours=max_age_hours,
        timeout=timeout,
        author_video_limit=author_video_limit,
        filters=filters,
    )
    return EventPayload(event_name="author_monitor_task", event_params=params)


def create_keyword_monitor_payload(
    batch_size: int = 20,
    max_age_hours: int = 3,
    timeout: int = 3600,
    keyword_video_limit: int = 100,
    keyword_search_days: int = 7,
    filters: Optional[Dict[str, Any]] = None,
) -> KeywordMonitorPayload:
    """创建关键词监控事件载荷"""
    params = KeywordMonitorParams(
        batch_size=batch_size,
        max_age_hours=max_age_hours,
        timeout=timeout,
        keyword_video_limit=keyword_video_limit,
        keyword_search_days=keyword_search_days,
        filters=filters,
    )
    return EventPayload(event_name="keyword_monitor_task", event_params=params)

    def to_json(self) -> str:
        """转换为 JSON 字符串"""
        return json.dumps(self.model_dump(), indent=2, default=str, ensure_ascii=False)
