"""
TrendInsight API 业务接口

基于新架构实现的 TrendInsight API 业务方法
使用 client_manager 管理的客户端发送请求，移除手动的参数组装逻辑
"""

from typing import Any, Dict, List, Optional

import httpx
from loguru import logger
from pydantic import ValidationError

from .client import client_manager
from .config import TrendInsightConfig
from .exceptions import (
    DataFetchError,
    TrendInsightAuthenticationError,
    TrendInsightRateLimitError,
    TrendInsightRequestError,
    TrendInsightResponseError,
    TrendInsightValidationError,
)
from .schemas import (
    AuthorDetailResponse,
    DarenSearchRequest,
    DarenSearchResponse,
    GreatUserTopVideoRequest,
    GreatUserTopVideoResponse,
    ItemIndexExistResponse,
    UserInfoResponse,
    VideoIndexResponse,
    VideoSearchResponse,
)

# ==================== 异步 API 方法 ====================


class AsyncTrendInsightAPI:
    """
    TrendInsight 异步 API 业务接口类

    提供所有 TrendInsight API 的异步业务方法
    """

    def __init__(
        self,
        async_client: httpx.AsyncClient = None,
        config: TrendInsightConfig = None,
    ):
        """
        初始化异步 API 接口

        Args:
            async_client: 异步 HTTP 客户端
            config: 配置对象
        """
        self.config = config or TrendInsightConfig()
        self.async_client = async_client or client_manager.create_async_client(config=self.config)

        logger.info("AsyncTrendInsightAPI 初始化完成")

    def _handle_response(self, response: httpx.Response) -> Dict[str, Any]:
        """
        处理响应数据

        Args:
            response: HTTP 响应对象

        Returns:
            解析后的响应数据

        Raises:
            各种 TrendInsight 异常
        """
        # 检查状态码
        if response.status_code == 401:
            raise TrendInsightAuthenticationError("认证失败，请检查 cookies")
        elif response.status_code == 429:
            raise TrendInsightRateLimitError("请求频率过高，请稍后重试")
        elif response.status_code >= 400:
            raise TrendInsightRequestError(f"请求失败: {response.status_code}", status_code=response.status_code)

        # 获取响应内容，处理编码问题
        try:
            # 尝试获取文本内容
            response_text = response.text
        except UnicodeDecodeError as e:
            # 如果UTF-8解码失败，尝试其他编码
            try:
                response_text = response.content.decode("gbk")
                logger.warning(f"异步响应使用GBK编码: {response.url}")
            except UnicodeDecodeError:
                try:
                    response_text = response.content.decode("latin-1")
                    logger.warning(f"异步响应使用Latin-1编码: {response.url}")
                except UnicodeDecodeError:
                    raise TrendInsightResponseError(f"异步响应编码解析失败: {e}")

        # 检查响应内容
        if not response_text or response_text.strip() == "" or response_text.strip() == "blocked":
            raise DataFetchError("账号被封禁或响应为空")

        # 检查是否是HTML错误页面
        if response_text.strip().startswith("<"):
            logger.error(f"异步收到HTML响应而非JSON: {response.url}")
            # 安全地记录响应内容
            try:
                logger.debug(f"异步响应内容前200字符: {response_text[:200]}")
            except (TypeError, AttributeError):
                logger.debug(f"异步响应内容: {response_text}")
            raise TrendInsightResponseError("服务器返回HTML页面而非JSON数据，可能是错误页面或重定向")

        # 尝试解析JSON
        try:
            import json

            data = json.loads(response_text)
        except json.JSONDecodeError as e:
            logger.error(f"异步JSON解析失败: {e}")
            # 安全地记录响应内容
            try:
                logger.debug(f"异步响应内容前200字符: {response_text[:200]}")
            except (TypeError, AttributeError):
                logger.debug(f"异步响应内容: {response_text}")
            raise TrendInsightResponseError(f"异步JSON解析失败: {e}")
        except Exception as e:
            logger.error(f"异步响应解析失败: {e}")
            # 安全地记录响应内容
            try:
                logger.debug(f"异步响应内容前200字符: {response_text[:200]}")
            except (TypeError, AttributeError):
                logger.debug(f"异步响应内容: {response_text}")
            raise TrendInsightResponseError(f"异步响应解析失败: {e}")

        return data

    async def query_user_self_info(self) -> UserInfoResponse:
        """
        查询用户自己的信息

        Mock file: rpc/trendinsight/tests/mock/query_user_self_info.json

        Returns:
            用户信息响应
        """
        uri = "/passport/account/info/v2/"

        try:
            response = await self.async_client.get(f"{self.config.base_url}{uri}")
            data = self._handle_response(response)
            return UserInfoResponse(**data)
        except ValidationError as e:
            raise TrendInsightValidationError(f"响应数据验证失败: {e}")

    async def query_daren_sug_great_user_list(self, request: DarenSearchRequest) -> DarenSearchResponse:
        """
        查询达人的信息

        Mock file: rpc/trendinsight/tests/mock/query_daren_sug_great_user_list.json

        Args:
            request: 达人搜索请求参数

        Returns:
            达人搜索响应
        """
        uri = "/api/v2/daren/get_sug_great_user_list"
        json_data = {"total": str(request.total), "keyword": request.keyword}

        try:
            response = await self.async_client.post(
                f"{self.config.base_url}{uri}", json=json_data, headers={"Content-Type": "application/json"}
            )
            data = self._handle_response(response)
            return DarenSearchResponse(**data)
        except ValidationError as e:
            raise TrendInsightValidationError(f"响应数据验证失败: {e}")

    async def search_info_by_keyword(
        self,
        keyword: str,
        author_ids: Optional[List[str]] = None,
        category_id: str = "0",
        date_type: int = 0,
        label_type: int = 0,
        duration_type: int = 0,
    ) -> VideoSearchResponse:
        """
        关键字搜索视频接口

        Mock file: rpc/trendinsight/tests/mock/search_info_by_keyword.json

        Args:
            keyword: 搜索关键词
            author_ids: 作者ID列表
            category_id: 分类ID
            date_type: 日期类型
            label_type: 标签类型
            duration_type: 时长类型

        Returns:
            视频搜索响应
        """
        uri = "/api/v2/index/itemQuery"
        json_data = {
            "query": keyword,
            "authorIds": author_ids or [],
            "categoryId": category_id,
            "dateType": date_type,
            "labelType": label_type,
            "durationType": duration_type,
        }

        try:
            response = await self.async_client.post(
                f"{self.config.base_url}{uri}", json=json_data, headers={"Content-Type": "application/json"}
            )
            data = self._handle_response(response)
            return VideoSearchResponse(**data)
        except ValidationError as e:
            raise TrendInsightValidationError(f"响应数据验证失败: {e}")

    async def get_author_detail(self, user_id: str) -> AuthorDetailResponse:
        """
        获取作者详情接口

        Mock file: rpc/trendinsight/tests/mock/get_author_detail.json

        Args:
            user_id: 用户ID

        Returns:
            作者详情响应
        """
        uri = "/api/v2/daren/get_author_info"
        json_data = {"user_id": user_id}

        try:
            # 设置特定的 Referer
            headers = {
                "Content-Type": "application/json",
                "Referer": f"https://trendinsight.oceanengine.com/arithmetic-index/daren/detail?uid={user_id}",
            }

            response = await self.async_client.post(f"{self.config.base_url}{uri}", json=json_data, headers=headers)
            data = self._handle_response(response)
            return AuthorDetailResponse(**data)
        except ValidationError as e:
            raise TrendInsightValidationError(f"响应数据验证失败: {e}")

    async def get_video_index(self, item_id: str, start_date: str, end_date: str) -> VideoIndexResponse:
        """
        获取视频指数数据接口

        Mock file: rpc/trendinsight/tests/mock/get_video_index.json

        Args:
            item_id: 视频ID
            start_date: 开始日期，格式：YYYYMMDD
            end_date: 结束日期，格式：YYYYMMDD

        Returns:
            视频指数响应
        """
        uri = "/api/v2/index/itemIndex"
        json_data = {"itemId": item_id, "startDate": start_date, "endDate": end_date}

        try:
            # 设置特定的 Referer
            headers = {
                "Content-Type": "application/json",
                "Referer": f"https://trendinsight.oceanengine.com/arithmetic-index/videoanalysis?id={item_id}",
            }

            response = await self.async_client.post(f"{self.config.base_url}{uri}", json=json_data, headers=headers)
            data = self._handle_response(response)
            return VideoIndexResponse(**data)
        except ValidationError as e:
            raise TrendInsightValidationError(f"响应数据验证失败: {e}")

    async def get_great_user_top_video(self, request: GreatUserTopVideoRequest) -> GreatUserTopVideoResponse:
        """
        获取优秀用户热门视频

        Mock file: rpc/trendinsight/tests/mock/get_great_user_top_video.json

        Args:
            request: 获取优秀用户热门视频请求参数

        Returns:
            优秀用户热门视频响应
        """
        uri = "/api/v2/daren/get_great_user_top_video"
        json_data = {
            "user_id": request.user_id,
            "start_date": request.start_date,
            "end_date": request.end_date,
        }

        try:
            # 设置特定的 Referer
            headers = {
                "Content-Type": "application/json",
                "Referer": f"https://trendinsight.oceanengine.com/arithmetic-index/daren/detail?uid={request.user_id}",
            }

            response = await self.async_client.post(f"{self.config.base_url}{uri}", json=json_data, headers=headers)
            data = self._handle_response(response)
            return GreatUserTopVideoResponse(**data)
        except ValidationError as e:
            raise TrendInsightValidationError(f"响应数据验证失败: {e}")

    async def get_item_index_exist(self, item_id: str) -> ItemIndexExistResponse:
        """
        检查视频指数是否存在

        Mock file: rpc/trendinsight/tests/mock/get_item_index_exist.json

        Args:
            item_id: 视频ID

        Returns:
            视频指数存在性检查响应
        """
        uri = "/api/v2/daren/item_index_exist"
        json_data = {"itemId": item_id}

        try:
            # 设置特定的 Referer
            headers = {
                "Content-Type": "application/json",
                "Referer": f"https://trendinsight.oceanengine.com/arithmetic-index/videoanalysis?id={item_id}",
            }

            response = await self.async_client.post(f"{self.config.base_url}{uri}", json=json_data, headers=headers)
            data = self._handle_response(response)
            return ItemIndexExistResponse(**data)
        except ValidationError as e:
            raise TrendInsightValidationError(f"响应数据验证失败: {e}")

    async def pong(self) -> bool:
        """
        测试接口是否可用

        Returns:
            是否可用
        """
        try:
            # 尝试调用用户信息接口来测试连接
            response = await self.query_user_self_info()

            # 检查响应是否有效
            if response and response.data:
                # 检查是否有用户ID，表示认证成功
                if (
                    hasattr(response.data, "user_id")
                    and response.data.user_id
                    and hasattr(response.data, "sec_user_id")
                    and response.data.sec_user_id
                ):
                    logger.debug("异步 pong 测试成功: 用户信息获取正常")
                    return True
                else:
                    logger.warning("异步 pong 测试失败: 用户信息不完整")
                    return False
            else:
                logger.warning("异步 pong 测试失败: 响应数据为空")
                return False

        except TrendInsightAuthenticationError:
            logger.error("异步 pong 测试失败: 认证失败")
            return False
        except TrendInsightRateLimitError:
            logger.error("异步 pong 测试失败: 请求频率过高")
            return False
        except Exception as e:
            logger.error(f"异步 pong 测试失败: {e}")
            return False


# 创建默认的异步 API 实例
async_api = AsyncTrendInsightAPI()
